<template>
  <view class="float-menu-container" ref="container">
    <view class="float-trigger" @click.stop="toggleMenu" ref="trigger" :style="triggerStyle">
      <slot name="trigger">
        <text>点击展开</text>
      </slot>
    </view>

    <view v-show="isVisible" class="float-menu" :class="{ 'float-menu-top': position === 'top' }" :style="menuStyle" ref="menu">
      <slot name="menu">
        <view class="float-menu-item" v-for="(item, index) in items" :key="index">
          {{ item }}
        </view>
      </slot>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  position: {
    type: String,
    default: 'top',
    validator: value => ['top', 'bottom'].includes(value)
  },
  sameWidth: {
    type: Boolean,
    default: true
  },
  offsetY: {
    type: Number,
    default: 16
  },
  offsetX: {
    type: Number,
    default: 0
  },
  triggerStyle: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['open', 'close', 'select']);

const isVisible = ref(false);
const trigger = ref(null);
const menu = ref(null);
const container = ref(null);
const triggerRect = ref({});

const menuStyle = computed(() => {
  const style = {
    'z-index': 1001,
    position: 'absolute',
    left: props.offsetX + 'rpx'
  };

  if (props.sameWidth && triggerRect.value.width) {
    style.width = triggerRect.value.width + 'rpx';
  }

  if (props.position === 'top') {
    style.bottom = `calc(100% + ${props.offsetY}rpx)`;
  } else {
    style.top = `calc(100% + ${props.offsetY}rpx)`;
  }

  return style;
});

const toggleMenu = async () => {
  isVisible.value = !isVisible.value;

  if (isVisible.value) {
    emit('open');
    await nextTick();
    updateTriggerRect();
    adjustPosition();
  } else {
    emit('close');
  }
};

const closeMenu = () => {
  if (isVisible.value) {
    isVisible.value = false;
    emit('close');
  }
};

const updateTriggerRect = () => {
  const query = uni.createSelectorQuery();
  query
    .select('.float-trigger')
    .boundingClientRect(data => {
      if (data) {
        triggerRect.value = {
          width: data.width,
          height: data.height,
          left: data.left,
          top: data.top
        };
      }
    })
    .exec();
};

const adjustPosition = () => {
  const query = uni.createSelectorQuery();
  query
    .select('.float-menu')
    .boundingClientRect(data => {
      if (!data) return;

      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      const windowWidth = systemInfo.windowWidth;

      const menuElement = menu.value?.$el || menu.value;
      if (!menuElement) return;

      if (props.position === 'bottom' && data.bottom > windowHeight) {
        menuElement.style.top = 'auto';
        menuElement.style.bottom = `calc(100% + ${props.offsetY}rpx)`;
      }

      if (data.right > windowWidth) {
        menuElement.style.left = 'auto';
        menuElement.style.right = '0rpx';
      }
    })
    .exec();
};

const handleClickOutside = event => {
  const containerElement = container.value?.$el || container.value;
  if (isVisible.value && containerElement && !containerElement.contains(event.target)) {
    closeMenu();
  }
};

const handleKeyDown = event => {
  if (event.key === 'Escape' && isVisible.value) {
    closeMenu();
  }
};

onMounted(() => {
  if (typeof document !== 'undefined') {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
  }
});

onBeforeUnmount(() => {
  if (typeof document !== 'undefined') {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
  }
});
</script>

<style scoped>
.float-menu-container {
  position: relative;
  display: inline-block;
}

.float-trigger {
  cursor: pointer;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.float-menu {
  position: absolute;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform-origin: center bottom;
  animation: float-show 0.2s ease-out;
  min-width: 240rpx;
}

.float-menu-item {
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
}

@keyframes float-show {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
