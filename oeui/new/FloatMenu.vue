<template>
  <view class="float-menu-container" ref="container">
    <!-- 触发元素 -->
    <view class="float-trigger" @click.stop="toggleMenu" ref="trigger" :style="triggerStyle">
      <slot name="trigger">
        <text>点击展开</text>
      </slot>
    </view>

    <!-- 浮动菜单 -->
    <view v-show="isVisible" class="float-menu" :class="{ 'float-menu-top': position === 'top' }" :style="menuStyle" ref="menu">
      <slot name="menu">
        <view class="float-menu-item" v-for="(item, index) in items" :key="index">
          {{ item }}
        </view>
      </slot>
    </view>

    <!-- 遮罩层 -->
    <view v-if="isVisible && showMask" class="float-mask" @click="closeMenu" :style="{ opacity: maskOpacity }"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';

const props = defineProps({
  // 菜单项数据
  items: {
    type: Array,
    default: () => []
  },
  // 显示位置：top（上方）/bottom（下方）
  position: {
    type: String,
    default: 'top',
    validator: value => ['top', 'bottom'].includes(value)
  },
  // 是否显示遮罩
  showMask: {
    type: Boolean,
    default: true
  },
  // 遮罩透明度
  maskOpacity: {
    type: Number,
    default: 0.5
  },
  // 菜单宽度是否与触发元素同宽
  sameWidth: {
    type: Boolean,
    default: true
  },
  // 垂直偏移量
  offsetY: {
    type: Number,
    default: 8
  },
  // 水平偏移量
  offsetX: {
    type: Number,
    default: 0
  },
  // 触发元素自定义样式
  triggerStyle: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['open', 'close', 'select']);

const isVisible = ref(false);
const trigger = ref(null);
const menu = ref(null);
const container = ref(null);

// 计算菜单样式
const menuStyle = computed(() => {
  const style = {
    'z-index': 1001,
    position: 'absolute',
    left: props.offsetX + 'px'
  };

  if (props.sameWidth && trigger.value) {
    style.width = uni.upx2px(trigger.value.$el.offsetWidth) + 'px';
  }

  if (props.position === 'top') {
    style.bottom = `calc(100% + ${props.offsetY}px)`;
  } else {
    style.top = `calc(100% + ${props.offsetY}px)`;
  }

  return style;
});

// 切换菜单显示状态
const toggleMenu = async () => {
  isVisible.value = !isVisible.value;

  if (isVisible.value) {
    emit('open');
    await nextTick();
    adjustPosition();
  } else {
    emit('close');
  }
};

// 关闭菜单
const closeMenu = () => {
  if (isVisible.value) {
    isVisible.value = false;
    emit('close');
  }
};

// 调整菜单位置（防止超出屏幕）
const adjustPosition = () => {
  if (!trigger.value || !menu.value) return;

  const query = uni.createSelectorQuery().in(this);
  query
    .select('.float-menu')
    .boundingClientRect(data => {
      if (!data) return;

      const windowHeight = uni.getSystemInfoSync().windowHeight;
      const windowWidth = uni.getSystemInfoSync().windowWidth;

      // 检查是否超出底部
      if (props.position === 'bottom' && data.bottom > windowHeight) {
        menu.value.style.top = 'auto';
        menu.value.style.bottom = `calc(100% + ${props.offsetY}px)`;
      }

      // 检查是否超出右侧
      if (data.right > windowWidth) {
        menu.value.style.left = 'auto';
        menu.value.style.right = '0';
      }
    })
    .exec();
};

// 点击外部关闭
const handleClickOutside = event => {
  if (isVisible.value && container.value && !container.value.$el.contains(event.target)) {
    closeMenu();
  }
};

// 处理键盘事件
const handleKeyDown = event => {
  if (event.key === 'Escape' && isVisible.value) {
    closeMenu();
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.float-menu-container {
  position: relative;
  display: inline-block;
}

.float-trigger {
  cursor: pointer;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.float-menu {
  position: absolute;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform-origin: center bottom;
  animation: float-show 0.2s ease-out;
  min-width: 120px;
}

.float-menu-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.float-menu-item:active {
  background-color: #f5f5f5;
}

.float-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
  z-index: 1000;
}

@keyframes float-show {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
